# React Form - Sign In/Sign Up

A modern login and registration functionality built with <PERSON>act featuring smooth animations and responsive design.

## Features

- Toggle between Sign In and Sign Up forms
- Smooth animations and transitions
- Responsive design
- Clean and modern UI
- Built with React hooks for state management

## Setup and Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

## Project Structure

```
src/
├── components/
│   └── AuthForm.jsx    # Main authentication form component
├── App.jsx             # Main App component
├── main.jsx           # React entry point
└── styles.css         # Styling
```

## Dependencies

- React 18.2.0
- React DOM 18.2.0
- Vite (for development and building)

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
